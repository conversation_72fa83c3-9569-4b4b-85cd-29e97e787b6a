<?php
require_once '../config.php';

$user = requireAdmin();

try {
    switch ($_SERVER['REQUEST_METHOD']) {
        case 'GET':
            handleGetUsers($user);
            break;
        case 'POST':
            handleCreateUser($user);
            break;
        case 'PUT':
            handleUpdateUser($user);
            break;
        case 'DELETE':
            handleDeleteUser($user);
            break;
        default:
            jsonResponse(['success' => false, 'message' => 'Method not allowed'], 405);
    }
} catch (Exception $e) {
    error_log("Users API Error: " . $e->getMessage());
    jsonResponse(['success' => false, 'message' => 'Lỗi server'], 500);
}

function handleGetUsers($admin) {
    global $pdo;
    
    $search = isset($_GET['search']) ? trim($_GET['search']) : '';
    $page = isset($_GET['page']) ? max(1, (int)$_GET['page']) : 1;
    $limit = isset($_GET['limit']) ? min(100, max(1, (int)$_GET['limit'])) : 20;
    $offset = ($page - 1) * $limit;
    
    $whereClause = '';
    $params = [];
    
    if ($search) {
        $whereClause = "WHERE username LIKE ? OR email LIKE ? OR nickname LIKE ?";
        $searchParam = "%$search%";
        $params = [$searchParam, $searchParam, $searchParam];
    }
    
    // Lấy tổng số users
    $countStmt = $pdo->prepare("SELECT COUNT(*) as total FROM users $whereClause");
    $countStmt->execute($params);
    $total = $countStmt->fetch()['total'];
    
    // Lấy danh sách users
    $stmt = $pdo->prepare("
        SELECT 
            id, username, email, nickname, signature, avatar, 
            is_admin, status, ip, location, last_login, 
            last_seen, created_at
        FROM users 
        $whereClause
        ORDER BY created_at DESC 
        LIMIT ? OFFSET ?
    ");
    $stmt->execute([...$params, $limit, $offset]);
    $users = $stmt->fetchAll();
    
    // Format dữ liệu
    $formattedUsers = [];
    foreach ($users as $user) {
        $formattedUsers[] = [
            'id' => (int)$user['id'],
            'username' => $user['username'],
            'email' => $user['email'],
            'nickname' => $user['nickname'],
            'signature' => $user['signature'],
            'avatar' => $user['avatar'],
            'is_admin' => (int)$user['is_admin'],
            'status' => (int)$user['status'],
            'ip' => $user['ip'],
            'location' => $user['location'],
            'last_login' => $user['last_login'],
            'last_seen' => $user['last_seen'],
            'created_at' => $user['created_at']
        ];
    }
    
    // Ghi log
    logAdminActivity($admin['id'], 'view_users', 'Xem danh sách người dùng');
    
    jsonResponse([
        'success' => true,
        'message' => 'Lấy danh sách người dùng thành công',
        'users' => $formattedUsers,
        'pagination' => [
            'total' => (int)$total,
            'page' => $page,
            'limit' => $limit,
            'pages' => ceil($total / $limit)
        ]
    ]);
}

function handleCreateUser($admin) {
    global $pdo;
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    $username = trim($input['username'] ?? '');
    $email = trim($input['email'] ?? '');
    $password = $input['password'] ?? '';
    $nickname = trim($input['nickname'] ?? '');
    $signature = trim($input['signature'] ?? '');
    $is_admin = isset($input['is_admin']) ? (int)$input['is_admin'] : 0;
    
    // Validate
    if (empty($username) || empty($email) || empty($password)) {
        jsonResponse(['success' => false, 'message' => 'Thiếu thông tin bắt buộc'], 400);
    }
    
    if (strlen($username) < 3 || strlen($username) > 50) {
        jsonResponse(['success' => false, 'message' => 'Tên đăng nhập từ 3-50 ký tự'], 400);
    }
    
    if (!filter_var($email, FILTER_VALIDATE_EMAIL)) {
        jsonResponse(['success' => false, 'message' => 'Email không hợp lệ'], 400);
    }
    
    if (strlen($password) < 6) {
        jsonResponse(['success' => false, 'message' => 'Mật khẩu tối thiểu 6 ký tự'], 400);
    }
    
    // Kiểm tra trùng lặp
    $stmt = $pdo->prepare("SELECT id FROM users WHERE username = ? OR email = ?");
    $stmt->execute([$username, $email]);
    if ($stmt->fetch()) {
        jsonResponse(['success' => false, 'message' => 'Tên đăng nhập hoặc email đã tồn tại'], 400);
    }
    
    // Tạo user
    $hashedPassword = password_hash($password, PASSWORD_DEFAULT);
    $stmt = $pdo->prepare("
        INSERT INTO users (username, email, password, nickname, signature, is_admin, ip) 
        VALUES (?, ?, ?, ?, ?, ?, ?)
    ");
    $stmt->execute([
        $username, $email, $hashedPassword, $nickname, 
        $signature, $is_admin, $_SERVER['REMOTE_ADDR']
    ]);
    
    $userId = $pdo->lastInsertId();
    
    // Ghi log
    logAdminActivity($admin['id'], 'create_user', "Tạo người dùng mới: $username", 'user', $userId);
    
    jsonResponse([
        'success' => true,
        'message' => 'Tạo người dùng thành công',
        'user_id' => (int)$userId
    ]);
}

function handleUpdateUser($admin) {
    global $pdo;
    
    $userId = isset($_GET['id']) ? (int)$_GET['id'] : 0;
    if (!$userId) {
        jsonResponse(['success' => false, 'message' => 'ID người dùng không hợp lệ'], 400);
    }
    
    $input = json_decode(file_get_contents('php://input'), true);
    
    $username = trim($input['username'] ?? '');
    $email = trim($input['email'] ?? '');
    $nickname = trim($input['nickname'] ?? '');
    $signature = trim($input['signature'] ?? '');
    $is_admin = isset($input['is_admin']) ? (int)$input['is_admin'] : 0;
    $status = isset($input['status']) ? (int)$input['status'] : 1;
    
    // Validate
    if (empty($username) || empty($email)) {
        jsonResponse(['success' => false, 'message' => 'Thiếu thông tin bắt buộc'], 400);
    }
    
    // Kiểm tra user tồn tại
    $stmt = $pdo->prepare("SELECT username FROM users WHERE id = ?");
    $stmt->execute([$userId]);
    $existingUser = $stmt->fetch();
    if (!$existingUser) {
        jsonResponse(['success' => false, 'message' => 'Người dùng không tồn tại'], 404);
    }
    
    // Kiểm tra trùng lặp (trừ chính user này)
    $stmt = $pdo->prepare("SELECT id FROM users WHERE (username = ? OR email = ?) AND id != ?");
    $stmt->execute([$username, $email, $userId]);
    if ($stmt->fetch()) {
        jsonResponse(['success' => false, 'message' => 'Tên đăng nhập hoặc email đã tồn tại'], 400);
    }
    
    // Cập nhật
    $stmt = $pdo->prepare("
        UPDATE users 
        SET username = ?, email = ?, nickname = ?, signature = ?, is_admin = ?, status = ?
        WHERE id = ?
    ");
    $stmt->execute([$username, $email, $nickname, $signature, $is_admin, $status, $userId]);
    
    // Ghi log
    logAdminActivity($admin['id'], 'update_user', "Cập nhật người dùng: $username", 'user', $userId);
    
    jsonResponse(['success' => true, 'message' => 'Cập nhật người dùng thành công']);
}

function handleDeleteUser($admin) {
    global $pdo;
    
    $userId = isset($_GET['id']) ? (int)$_GET['id'] : 0;
    if (!$userId) {
        jsonResponse(['success' => false, 'message' => 'ID người dùng không hợp lệ'], 400);
    }
    
    // Không cho phép xóa chính mình
    if ($userId == $admin['id']) {
        jsonResponse(['success' => false, 'message' => 'Không thể xóa chính mình'], 400);
    }
    
    // Kiểm tra user tồn tại
    $stmt = $pdo->prepare("SELECT username FROM users WHERE id = ?");
    $stmt->execute([$userId]);
    $user = $stmt->fetch();
    if (!$user) {
        jsonResponse(['success' => false, 'message' => 'Người dùng không tồn tại'], 404);
    }
    
    // Xóa user
    $stmt = $pdo->prepare("DELETE FROM users WHERE id = ?");
    $stmt->execute([$userId]);
    
    // Ghi log
    logAdminActivity($admin['id'], 'delete_user', "Xóa người dùng: {$user['username']}", 'user', $userId);
    
    jsonResponse(['success' => true, 'message' => 'Xóa người dùng thành công']);
}

function logAdminActivity($adminId, $action, $description, $targetType = null, $targetId = null) {
    global $pdo;
    $stmt = $pdo->prepare("INSERT INTO admin_activities (admin_id, action, target_type, target_id, description, ip) VALUES (?, ?, ?, ?, ?, ?)");
    $stmt->execute([$adminId, $action, $targetType, $targetId, $description, $_SERVER['REMOTE_ADDR']]);
}
